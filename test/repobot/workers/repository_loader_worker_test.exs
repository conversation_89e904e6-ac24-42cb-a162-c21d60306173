defmodule Repobot.Workers.RepositoryLoaderWorkerTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  import Repobot.Test.Fixtures
  import Mox

  alias Repobot.Workers.RepositoryLoaderWorker
  alias Repobot.{Repositories, Accounts}

  setup :verify_on_exit!

  describe "perform/1" do
    test "successfully loads repositories when cache is empty" do
      user = user_fixture()
      organization = organization_fixture(user)

      # Mock the GitHub API to return repositories
      expect(Repobot.Test.GitHubMock, :user_repos, fn _client, _username ->
        {:ok,
         [
           %{
             "id" => 123,
             "name" => "test-repo",
             "full_name" => "#{user.login}/test-repo",
             "owner" => %{"login" => user.login},
             "language" => "Elixir",
             "fork" => false,
             "private" => false,
             "description" => "Test repository"
           }
         ]}
      end)

      job_args = %{
        "user_id" => user.id,
        "organization_id" => organization.id
      }

      assert :ok = perform_job(RepositoryLoaderWorker, job_args)

      # Verify repositories were created
      repositories = Repositories.user_repositories(user, false, organization.id)
      assert length(repositories) == 1
      assert hd(repositories).name == "test-repo"
    end

    test "returns cached repositories when available" do
      user = user_fixture()
      organization = organization_fixture(user)

      # Create a repository in the cache
      create_repository(%{
        name: "cached-repo",
        full_name: "#{user.login}/cached-repo",
        owner: user.login,
        organization_id: organization.id
      })

      job_args = %{
        "user_id" => user.id,
        "organization_id" => organization.id
      }

      assert :ok = perform_job(RepositoryLoaderWorker, job_args)

      # Verify cached repository is returned
      repositories = Repositories.user_repositories(user, false, organization.id)
      assert length(repositories) == 1
      assert hd(repositories).name == "cached-repo"
    end

    test "sends progress updates when receiver is provided" do
      user = user_fixture()
      organization = organization_fixture(user)

      # Mock the GitHub API
      expect(Repobot.Test.GitHubMock, :user_repos, fn _client, _username ->
        {:ok, []}
      end)

      # Encode the current process as receiver
      receiver = Repobot.Workers.Worker.encode_pid(self())

      job_args = %{
        "user_id" => user.id,
        "organization_id" => organization.id,
        "receiver" => receiver
      }

      assert :ok = perform_job(RepositoryLoaderWorker, job_args)

      # Verify progress messages were sent
      assert_received {:repository_loading_progress, 10, "Starting repository refresh..."}
      assert_received {:repository_loading_progress, 30, "Fetching repositories from GitHub..."}
      assert_received {:repository_loading_complete, {:ok, []}}
    end

    test "handles GitHub API errors gracefully" do
      user = user_fixture()
      organization = organization_fixture(user)

      # Mock the GitHub API to return an error
      expect(Repobot.Test.GitHubMock, :user_repos, fn _client, _username ->
        {:error, "API rate limit exceeded"}
      end)

      job_args = %{
        "user_id" => user.id,
        "organization_id" => organization.id
      }

      assert {:error, _reason} = perform_job(RepositoryLoaderWorker, job_args)
    end

    test "fails with invalid job arguments" do
      job_args = %{"invalid" => "args"}

      assert {:error, reason} = perform_job(RepositoryLoaderWorker, job_args)
      assert reason =~ "missing required user_id or organization_id"
    end
  end

  describe "enqueue_repository_loading/3" do
    test "successfully enqueues a job" do
      user = user_fixture()
      organization = organization_fixture(user)

      assert {:ok, %Oban.Job{}} =
               RepositoryLoaderWorker.enqueue_repository_loading(user.id, organization.id)
    end

    test "enqueues job with receiver" do
      user = user_fixture()
      organization = organization_fixture(user)

      assert {:ok, %Oban.Job{args: args}} =
               RepositoryLoaderWorker.enqueue_repository_loading(user.id, organization.id, self())

      assert Map.has_key?(args, "receiver")
    end
  end
end
