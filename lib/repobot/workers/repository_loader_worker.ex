defmodule Repobot.Workers.RepositoryLoaderWorker do
  @moduledoc """
  Oban worker for loading/refreshing user repositories from GitHub in the background.

  This worker handles the repository loading logic that was previously done synchronously
  in the onboarding flow, making it non-blocking and providing progress feedback.
  """

  use Repobot.Workers.Worker, queue: :sync, max_attempts: 3

  alias Repobot.{Repositories, Accounts.User, Accounts.Organization}

  @impl Oban.Worker
  def perform(
        %Oban.Job{args: %{"user_id" => user_id, "organization_id" => organization_id} = args} =
          job
      ) do
    log_job_start(job)

    # Get the receiver for progress updates
    receiver = Map.get(args, "receiver")

    try do
      # Load user and organization
      user = Repobot.Repo.get!(User, user_id)
      _organization = Repobot.Repo.get!(Organization, organization_id)

      # Send initial progress update
      send_progress_update(receiver, 10, "Starting repository refresh...")

      # Check if repositories exist in cache
      cached_repos = Repositories.user_repositories(user, false, organization_id)

      if Enum.empty?(cached_repos) do
        # No cached repositories, need to refresh from GitHub
        send_progress_update(receiver, 30, "Fetching repositories from GitHub...")

        case refresh_repositories(user, organization_id, receiver) do
          {:ok, repositories} ->
            send_progress_update(receiver, 90, "Repository refresh complete")

            # Send completion notification
            send_completion_notification(receiver, {:ok, repositories})

            log_job_success(job, %{
              repositories_count: length(repositories),
              organization_id: organization_id
            })

            :ok

          {:error, reason} ->
            send_completion_notification(receiver, {:error, reason})
            log_job_error(job, reason)
            {:error, reason}
        end
      else
        # Repositories already cached, return them quickly
        send_progress_update(receiver, 90, "Using cached repositories")
        send_completion_notification(receiver, {:ok, cached_repos})

        log_job_success(job, %{
          repositories_count: length(cached_repos),
          organization_id: organization_id,
          cached: true
        })

        :ok
      end
    rescue
      e ->
        reason = Exception.message(e)
        send_completion_notification(receiver, {:error, reason})
        log_job_error(job, reason, %{exception: inspect(e)})
        {:error, reason}
    end
  end

  def perform(%Oban.Job{} = job) do
    log_job_start(job)

    reason = "Invalid job arguments - missing required user_id or organization_id"
    log_job_error(job, reason, %{received_args: job.args})
    {:error, reason}
  end

  # Public API for enqueuing repository loading jobs

  @doc """
  Enqueues a repository loading job for the given user and organization.

  ## Parameters
  - user_id: The ID of the user whose repositories to load
  - organization_id: The ID of the organization context
  - receiver: Optional receiver for progress updates (PID or encoded PID)

  ## Returns
  {:ok, %Oban.Job{}} on success, {:error, reason} on failure
  """
  def enqueue_repository_loading(user_id, organization_id, receiver \\ nil) do
    args = %{
      "user_id" => user_id,
      "organization_id" => organization_id
    }

    args = if receiver, do: Map.put(args, "receiver", encode_receiver(receiver)), else: args

    new(args)
    |> Oban.insert()
  end

  # Private helper functions

  defp refresh_repositories(user, organization_id, receiver) do
    try do
      send_progress_update(receiver, 40, "Connecting to GitHub API...")

      # Use the existing repository refresh logic
      repositories = Repositories.user_repositories(user, :refresh, organization_id)

      send_progress_update(receiver, 80, "Processing #{length(repositories)} repositories...")

      {:ok, repositories}
    rescue
      e ->
        {:error, Exception.message(e)}
    end
  end

  defp send_progress_update(nil, _progress, _message), do: :ok

  defp send_progress_update(receiver, progress, message) do
    safe_send(receiver, {:repository_loading_progress, progress, message})
  end

  defp send_completion_notification(nil, _result), do: :ok

  defp send_completion_notification(receiver, result) do
    safe_send(receiver, {:repository_loading_complete, result})
  end

  defp encode_receiver(receiver) when is_pid(receiver) do
    encode_pid(receiver)
  end

  defp encode_receiver({pid, component}) when is_pid(pid) and is_atom(component) do
    {encode_pid(pid), Atom.to_string(component)}
  end

  defp encode_receiver(receiver), do: receiver
end
